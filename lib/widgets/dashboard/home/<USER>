import 'package:flutter/material.dart';
import 'package:nds_app/models/user_info.dart';
import 'package:nds_app/companies/scan_and_fuel_saving_factory.dart';

class ScanAndFuelSaving extends StatelessWidget {
  final Function() action;
  final UserInfo? userInfo;
  const ScanAndFuelSaving(
      {super.key, required this.action, required this.userInfo});

  @override
  Widget build(BuildContext context) {
    return ScanAndFuelSavingFactory.createScanAndFuelSaving(
      action: action,
      userInfo: userInfo,
    );
  }
}
