import 'package:flutter/material.dart';
import 'package:nds_app/companies/action_button_row_factory.dart';

class ActionButtonRow extends StatelessWidget {
  final bool isConnected;
  final Function connectAction;
  final BuildContext context;
  final String userName;
  final bool isDashboardActionRow;
  const ActionButtonRow(
      {super.key,
      required this.isConnected,
      required this.connectAction,
      required this.context,
      required this.userName,
      required this.isDashboardActionRow});

  @override
  Widget build(BuildContext context) {
    return ActionButtonRowFactory.createActionButtonRow(
      isConnected: isConnected,
      connectAction: connectAction,
      context: context,
      userName: userName,
      isDashboardActionRow: isDashboardActionRow,
    );
  }
}
