import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/companies/b2c/widgets/dashboard/action_button_row_b2c.dart';
import 'package:nds_app/companies/lapa/widgets/dashboard/action_button_row_lapa.dart';
import 'package:nds_app/companies/nds/widgets/dashboard/action_button_row_nds.dart';
import 'package:nds_app/companies/prodred/widgets/dashboard/action_button_row_prodred.dart';
import 'package:nds_app/companies/nichesolv/widgets/dashboard/action_button_row_nichesolv.dart';

class ActionButtonRowFactory {
  static Widget createActionButtonRow({
    required bool isConnected,
    required Function connectAction,
    required BuildContext context,
    required String userName,
    required bool isDashboardActionRow,
  }) {
    switch (companyName) {
      case 'b2c':
        return ActionButtonRowB2C(
          isConnected: isConnected,
          connectAction: connectAction,
          context: context,
          userName: userName,
          isDashboardActionRow: isDashboardActionRow,
        );
      case 'lapa':
        return ActionButtonRowLapa(
          isConnected: isConnected,
          connectAction: connectAction,
          context: context,
          userName: userName,
          isDashboardActionRow: isDashboardActionRow,
        );
      case 'nds':
        return ActionButtonRowNDS(
          isConnected: isConnected,
          connectAction: connectAction,
          context: context,
          userName: userName,
          isDashboardActionRow: isDashboardActionRow,
        );
      case 'prodred':
        return ActionButtonRowProdRed(
          isConnected: isConnected,
          connectAction: connectAction,
          context: context,
          userName: userName,
          isDashboardActionRow: isDashboardActionRow,
        );
      case 'nichesolv':
        return ActionButtonRowNichesolv(
          isConnected: isConnected,
          connectAction: connectAction,
          context: context,
          userName: userName,
          isDashboardActionRow: isDashboardActionRow,
        );
      default:
        return ActionButtonRowNDS(
          isConnected: isConnected,
          connectAction: connectAction,
          context: context,
          userName: userName,
          isDashboardActionRow: isDashboardActionRow,
        ); // Default fallback
    }
  }
}
