import 'dart:convert';
import 'package:bloc/bloc.dart';
import 'package:nds_app/companies/common/vehicle_health_model.dart';

import '../../constant/api_urls.dart';
import '../../services/api_service.dart';

abstract class VehicleHealthEvent {}
class FetchVehicleHealth extends VehicleHealthEvent {
  final String imei;
  FetchVehicleHealth(this.imei);
}

abstract class VehicleHealthState {}
class VehicleHealthInitial extends VehicleHealthState {}
class VehicleHealthLoading extends VehicleHealthState {}
class VehicleHealthLoaded extends VehicleHealthState {
  final List<VehiclePart> parts;
  final Map<String, ProfileImageData> profileImages;
  VehicleHealthLoaded(this.parts, this.profileImages);
}
class VehicleHealthError extends VehicleHealthState {
  final String message;
  VehicleHealthError(this.message);
}

class VehicleHealthBloc extends Bloc<VehicleHealthEvent, VehicleHealthState> {
  VehicleHealthBloc() : super(VehicleHealthInitial()) {
    on<FetchVehicleHealth>((event, emit) async {
      emit(VehicleHealthLoading());
      try {
        final response = await BackendApi.initiateGetCall(
            ApiUrls.vehicleHealth,
            params: {"imei": event.imei});
        final jsonData = json.decode(response.body);
        final parts = (jsonData['parts'] as List)
            .map((e) => VehiclePart.fromJson(e))
            .toList();
        final profileImages = (jsonData['profileImages'] as Map<String, dynamic>)
            .map((key, value) => MapEntry(key, ProfileImageData.fromJson(value)));

        emit(VehicleHealthLoaded(parts, profileImages));
      } catch (e) {
        emit(VehicleHealthError(e.toString()));
      }
    });
  }
}