import 'package:flutter/material.dart';
import 'package:nds_app/companies/b2c/views/home/<USER>';
import 'package:nds_app/companies/lapa/views/home/<USER>';
import 'package:nds_app/companies/nds/views/home/<USER>';
import 'package:nds_app/companies/nichesolv/views/home/<USER>';
import 'package:nds_app/companies/prodred/views/home/<USER>';
import '../branding/branding.dart';

/// Factory class to create company-specific home views based on the current company configuration
class HomeViewFactory {

  static Widget createHomeView({required Function dashboardAction}) {
    switch (companyName) {
      case 'b2c':
        return HomeViewB2C(dashboardAction: dashboardAction);
      case 'lapa':
        return HomeViewLapa(dashboardAction: dashboardAction);
      case 'nds':
        return HomeViewNDS(dashboardAction: dashboardAction);
      case 'prodred':
        return HomeViewProdRed(dashboardAction: dashboardAction);
      case 'Nichesolv':
        return HomeViewNichesolv(dashboardAction: dashboardAction);
      default:
        // Default to NDS if company is not recognized
        return HomeViewNDS(dashboardAction: dashboardAction);
    }
  }

  /// Returns the company-specific home view class name for debugging purposes
  static String getHomeViewClassName() {
    switch (companyName) {
      case 'b2c':
        return 'HomeViewB2C';
      case 'lapa':
        return 'HomeViewLapa';
      case 'nds':
        return 'HomeViewNDS';
      case 'prodred':
        return 'HomeViewProdRed';
      case 'Nichesolv':
        return 'HomeViewNichesolv';
      default:
        return 'HomeViewNDS';
    }
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'Nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
