import 'package:flutter/material.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/models/user_info.dart';
import 'package:nds_app/companies/b2c/widgets/dashboard/home/<USER>';
import 'package:nds_app/companies/lapa/widgets/dashboard/home/<USER>';
import 'package:nds_app/companies/nds/widgets/dashboard/home/<USER>';
import 'package:nds_app/companies/prodred/widgets/dashboard/home/<USER>';
import 'package:nds_app/companies/nichesolv/widgets/dashboard/home/<USER>';

class ScanAndFuelSavingFactory {
  static Widget createScanAndFuelSaving({
    required Function() action,
    required UserInfo? userInfo,
  }) {
    switch (companyName) {
      case 'b2c':
        return ScanAndFuelSavingB2C(
          action: action,
          userInfo: userInfo,
        );
      case 'lapa':
        return ScanAndFuelSavingLapa(
          action: action,
          userInfo: userInfo,
        );
      case 'nds':
        return ScanAndFuelSavingNDS(
          action: action,
          userInfo: userInfo,
        );
      case 'prodred':
        return ScanAndFuelSavingProdRed(
          action: action,
          userInfo: userInfo,
        );
      case 'nichesolv':
        return ScanAndFuelSavingNichesolv(
          action: action,
          userInfo: userInfo,
        );
      default:
        return ScanAndFuelSavingNDS(
          action: action,
          userInfo: userInfo,
        ); // Default fallback
    }
  }
}
