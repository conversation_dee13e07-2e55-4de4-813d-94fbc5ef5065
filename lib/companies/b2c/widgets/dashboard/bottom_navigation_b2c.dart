import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_bloc.dart';
import 'package:nds_app/blocs/sctoor_access/drop_down/edit_rider_drop_down_event.dart';
import 'package:nds_app/branding/branding.dart';
import 'package:nds_app/common/colors.dart';
import 'package:nds_app/common/constant.dart';
import 'package:nds_app/common/dimensions.dart';
import 'package:nds_app/constant/action.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/main.dart';
import 'package:nds_app/models/enums/color_type.dart';
import 'package:nds_app/companies/insights_factory.dart';
import 'package:nds_app/companies/select_fleet_factory.dart';
import 'package:nds_app/screens/profile/profile.dart';
import 'package:nds_app/screens/vehicle/vehicle.dart';

import '../../../../blocs/navigation/navigation_bar_stream.dart';
import '../../../../common/image_urls.dart';
import '../../../../screens/HomePage/home.dart';

final NavigationBarStream _bloc = NavigationBarStream();

Widget getBodyB2C(
    Function action, BuildContext context, Color color, ColorType colorType) {
  Dimensions dimensions = Dimensions(context);

  List<Widget> widgets = [
    HomeScreen(
      dashboardAction: action,
    ),
    Padding(
      padding: EdgeInsets.only(
          left: 20 / 414 * dimensions.width,
          right: 20 / 414 * dimensions.width),
      child: Vehicle(color: color, colorType: colorType),
    ),
    InsightsFactory.createInsights(color, colorType),
    Padding(
      padding: EdgeInsets.only(
          left: 0 * dimensions.width, right: 0 * dimensions.width),
      child: const ProfileScreen(),
    ),
  ];

  return StreamBuilder<int>(
      stream: _bloc.navigationBar,
      builder: (blocContext, snapshot) {
        OverlayEntry? entry =
            context.read<EditRiderDropDownBloc>().state.overlayEntry;
        if (entry != null) {
          entry.remove();
          entry = null;
          context
              .read<EditRiderDropDownBloc>()
              .add(const EditRiderDropDownEvent(overlayEntry: null));
        }
        return InkWell(
            splashFactory: NoSplash.splashFactory,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onTap: () {
              OverlayEntry? entry =
                  context.read<EditRiderDropDownBloc>().state.overlayEntry;
              if (entry != null) {
                entry.remove();
                entry = null;
                context
                    .read<EditRiderDropDownBloc>()
                    .add(const EditRiderDropDownEvent(overlayEntry: null));
              }
            },
            child: widgets[snapshot.data ?? bottomNavigationIndex]);
      });
}

Widget getCustomBottomNavigationBarB2C(
    BuildContext context, Function action, Color color, ColorType colorType) {
  Dimensions dimensions = Dimensions(context);

  ThemeMode themeMode = MyApp.of(context).getCurrentThemeMode();
  _bloc.submitIndex(bottomNavigationIndex);

  return Stack(
    alignment: AlignmentDirectional.bottomCenter,
    children: [
      Padding(
        padding: EdgeInsets.only(bottom: 9 / 896 * dimensions.height),
        child: Container(
          width: dimensions.width * 390 / 414,
          height: dimensions.height * 88 / 896,
          padding: EdgeInsets.all(dimensions.width * 8 / 414),
          // Dynamic padding
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            //color:   color == colorGrey800 ? loginThemeColor : color,// Transparent background
            border: Border.all(color: colorGrey300, width: 1.5), // Grey border
            borderRadius: BorderRadius.all(
              Radius.circular(44 / 414 * dimensions.width),
            ),
          ),
          child: Container(
            width: dimensions.width * 360 / 414,
            height: dimensions.height * 58 / 896,
            decoration: BoxDecoration(
                color: Theme.of(context).primaryColorLight,
                borderRadius: BorderRadius.all(
                    Radius.circular(32 / 414 * dimensions.width)),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).indicatorColor,
                    offset: const Offset(
                      1.0,
                      1.0,
                    ),
                    blurRadius: 1.0,
                    spreadRadius: 1.0,
                  ),
                ]),
            child: FittedBox(
              child: Row(
                children: getItemsB2C(
                    action,
                    dimensions,
                    color == colorGrey800 ? colorWhite : color,
                    themeMode,
                    colorType),
              ),
            ),
          ),
        ),
      ),
      Visibility(
        visible: !isB2CUser,
        child: Padding(
          // padding: EdgeInsets.only(bottom: dimensions.height * 28 / 896),
          padding: const EdgeInsets.symmetric(vertical: 20),
          child: Stack(
            children: [
              Container(
                height: 58 / 414 * dimensions.width,
                width: 58 / 414 * dimensions.width,
                decoration: BoxDecoration(
                  color: themeMode == ThemeMode.dark
                      ? colorBackgroundDarkMode
                      : Colors.white,
                  border: Border.all(color: colorGrey300, width: 1.5),
                  // Grey border
                  borderRadius: BorderRadius.all(
                    Radius.circular(44 / 414 * dimensions.width),
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.all(7 / 414 * dimensions.width),
                child: Container(
                  width: 44 / 414 * dimensions.width,
                  height: 44 / 414 * dimensions.width,
                  decoration: BoxDecoration(
                    color: themeMode == ThemeMode.dark
                        ? colorDefaultVehicleThemeDark
                        : colorDefaultVehicleThemeLight,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
              Opacity(
                opacity: ((currentVehicleStatus == VehicleStatus.connected)
                    ? 0.2
                    : 1),
                child: Padding(
                  padding: EdgeInsets.only(
                      left: 18 / 414 * dimensions.width,
                      top: 18 / 896 * dimensions.height),
                  child: InkWell(
                    splashFactory: NoSplash.splashFactory,
                    onTap: () async {
                      if (currentVehicleStatus == VehicleStatus.disconnected) {
                        // ignore: use_build_context_synchronously
                        Navigator.of(context).push(
                          MaterialPageRoute(
                              builder: (context) =>
                                  SelectFleetFactory.createSelectFleet()),
                        );
                      } else {
                        await DialogAction.disconnect.action(context: context);
                      }
                    },
                    child: SizedBox(
                      width: 24 / 414 * dimensions.width,
                      height: 24 / 896 * dimensions.height,
                      child: SvgPicture.asset(bottomNavigationImages['scan']!,
                          // ignore: deprecated_member_use
                          color: themeMode == ThemeMode.dark
                              ? colorDefaultVehicleThemeLight
                              : colorDefaultVehicleThemeDark),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    ],
  );
}

List<Widget> _itemsB2C = [];

List<Widget> getItemsB2C(Function action, Dimensions dimensions,
    Color iconColor, ThemeMode themeMode, ColorType colorType) {
  List<String> unselectedImageUrls = [
    bottomNavigationImages['home']!,
    bottomNavigationImages['vehicle']!,
    bottomNavigationImages['insights']!,
    bottomNavigationImages['profile']!,
  ];

  _itemsB2C = [];
  for (int i = 0; i < unselectedImageUrls.length; i++) {
    if (isB2CUser && i == 0) {
      _itemsB2C.add(SizedBox(
        width: 20 / 414 * dimensions.width,
      ));
    }
    _itemsB2C.add(
      InkWell(
          onTap: () {
            bottomNavigationIndex = i;
            _bloc.submitIndex(bottomNavigationIndex);

            //  action.call();
          },
          child: StreamBuilder(
            stream: _bloc.navigationBar,
            builder: (context, snapshot) {
              int? index = bottomNavigationIndex;
              Color vehicleColor =
                  currentVehicleStatus == VehicleStatus.connected
                      ? colorType == ColorType.light
                          ? Theme.of(context).highlightColor
                          : iconColor
                      : loginThemeColor;

              if (snapshot.data != null) {
                index = snapshot.data;
              }
              return i == index
                  ? Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Stack(
                        alignment: AlignmentDirectional.center,
                        children: [
                          Container(
                            height: 48 / 414 * dimensions.width,
                            width: 48 / 414 * dimensions.width,
                            decoration: BoxDecoration(
                              color: vehicleColor,
                              borderRadius: BorderRadius.all(
                                Radius.circular(44 / 414 * dimensions.width),
                              ),
                            ),
                          ),
                          SvgPicture.asset(
                            unselectedImageUrls[i],
                            colorFilter: ColorFilter.mode(
                                (vehicleColor.computeLuminance() < 0.5
                                    ? Colors.white
                                    : Colors.black),
                                BlendMode.srcIn),
                          ),
                        ],
                      ),
                    )
                  : Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: 15 / 414 * dimensions.width),
                      child: SvgPicture.asset(unselectedImageUrls[i],
                          // ignore: deprecated_member_use
                          color: colorType == ColorType.light
                              ? colorWhite
                              : Theme.of(context).primaryColorDark),
                    );
            },
          )),
    );
    if (isB2CUser) {
      _itemsB2C.add(SizedBox(
        width: 30 / 414 * dimensions.width,
      ));
    }

    if (i == ((unselectedImageUrls.length / 2) - 1)) {
      _itemsB2C.add(SizedBox(
        width: (isB2CUser ? 0 : 80) / 414 * dimensions.width,
      ));
    }
  }
  return _itemsB2C;
}
