import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:nds_app/common/strings.dart';
import 'package:nds_app/constant/vehicle_status.dart';
import 'package:nds_app/services/log_screen_tracking_event.dart';
import 'package:nds_app/streams/vehicle_data.dart';
import 'package:nds_app/streams/vehicle_status_data.dart';
import 'package:http/http.dart' as http;
import 'package:nds_app/widgets/dashboard/home/<USER>';
import 'package:nds_app/widgets/dashboard/home/<USER>';
import 'package:nds_app/widgets/dashboard/home/<USER>';
import 'package:nds_app/widgets/dashboard/home/<USER>';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../../common/constant.dart';
import '../../../../common/dimensions.dart';
import '../../../../common/image_urls.dart';
import '../../../../common/shared_preferences_keys.dart';
import '../../../../constant/action.dart';
import '../../../../constant/api_urls.dart';
import '../../../../models/user_info.dart';
import '../../../../models/vehicle_info.dart';
import '../../../../services/api_service.dart';

class HomeViewProdRed extends StatefulWidget {
  final Function dashboardAction;
  const HomeViewProdRed({
    super.key,
    required this.dashboardAction,
  });
  @override
  State<HomeViewProdRed> createState() => _HomeViewProdRedState();
}

class _HomeViewProdRedState extends State<HomeViewProdRed> {
  VehicleInfo vehicleInfo = VehicleInfo();
  late String homeUserDisplayName;
  late String imageUrl;
  late ScrollController _scrollController;
  VehicleStatusDataStream vehicleStatusDataStream = VehicleStatusDataStream();
  VehicleDataStream vehicleDataStream = VehicleDataStream();
  Timer? _scrollLogTimer;
  LogScreenTrackingEvent logScreenTrackingEvent = LogScreenTrackingEvent();

  @override
  void initState() {
    LogScreenTrackingEvent()
        .logScreenView(eventName: 'screen_view', parameters: {
      'screen_screen': 'ProdRed Home Screen',
      'screen_class': widget.runtimeType.toString(),
    });
    imageUrl = "";
    homeUserDisplayName = homeDisplayNameOptions[0];

    _scrollController = ScrollController();
    _scrollController.addListener(_scrollListener);
    super.initState();
  }

  _scrollListener() {
    if (_scrollLogTimer?.isActive ?? false) return;
    _scrollLogTimer = Timer(const Duration(seconds: 1), () {
      logScreenTrackingEvent.logScreenView(
          eventName: trackingLabels['ScrollHomeScreenAction']!);
    });
  }

  @override
  Widget build(BuildContext context) {
    Dimensions dimensions = Dimensions(context);
    return FutureBuilder(
      builder: (context, snapshot) {
        Widget widget = Center(
          child: Image.asset(
            isTwoWheels
                ? loaderGifImages['2Wheels']!
                : loaderGifImages['3Wheels']!,
          ),
        );

        if (snapshot.connectionState == ConnectionState.done) {
          widget = SizedBox(
            height: 775 / 896 * dimensions.height,
            child: Scrollbar(
              controller: _scrollController,
              radius: const Radius.circular(20),
              thickness: 5,
              child: SingleChildScrollView(
                controller: _scrollController,
                scrollDirection: Axis.vertical,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(
                          left: 14 / 414 * dimensions.width,
                          right: 14 / 414 * dimensions.width),
                      child: Column(
                        children: [
                          // ProdRed specific user and vehicle name
                          UserAndVehicleName(
                            currentVehicleStatus: currentVehicleStatus,
                            vehicleInfo: vehicleInfo,
                            firstName: userInfo?.firstName ?? "",
                          ),
                          // Show scan and fuel saving when disconnected
                          Visibility(
                            visible: currentVehicleStatus ==
                                VehicleStatus.disconnected,
                            child: ScanAndFuelSaving(
                                action: () {}, userInfo: userInfo),
                          ),
                          // Show battery and riding modes when connected
                          Visibility(
                            visible: currentVehicleStatus ==
                                VehicleStatus.connected,
                            child: BatteryAndRidingModes(
                              vehicleInfo: vehicleInfo,
                              userInfo: userInfo,
                            ),
                          ),
                          SizedBox(
                            height: 16 / 896 * dimensions.height,
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: 6 / 414 * dimensions.width),
                            child: const MapAndAvailableVehicle(),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }
        return widget;
      },
      future: loadData(),
    );
  }

  loadData() async {
    SharedPreferences pref = await SharedPreferences.getInstance();
    JsonDecoder decoder = const JsonDecoder();

    if (userInfo == null) {
      userInfo = await getUserInfo(decoder);
      userName = userInfo?.firstName ?? "";

      if (userInfo?.connectedVehicleImei != null) {
        pref.setString(
            connectedVehicleImeiNo, userInfo?.connectedVehicleImei! ?? "");
        currentVehicleStatus = VehicleStatus.connected;
        int statusCode = await DialogAction.vehicleInfo
            .action(imei: userInfo?.connectedVehicleImei);

        if (statusCode != 200) {
          isVehicleInfoAlertMessageExist = true;
        }

        vehicleStatusDataStream
            .updateVehicleStatusResponse(VehicleStatus.connected);
        if (vehicleInfoConstant != null) {
          vehicleInfo = vehicleInfoConstant ?? VehicleInfo();
        } else {
          vehicleInfo = VehicleInfo();
        }
        vehicleDataStream.updateVehicleInfo(vehicleInfo);
      }
    } else {
      if (userInfo?.connectedVehicleImei != "" &&
          userInfo?.connectedVehicleImei != null) {
        await DialogAction.vehicleInfo
            .action(imei: userInfo?.connectedVehicleImei);
      }
      
      // ProdRed specific logic - use existing vehicle info
      vehicleInfo = vehicleInfoConstant!;
    }
    
    homeUserDisplayName =
        pref.getString(homeDisplayNameKey) ?? homeDisplayNameOptions[0];

    vehicleInfo.images?.forEach(
      (element) {
        if (element.tag == defaultImageTag) {
          imageUrl = element.url!;
        }
      },
    );
  }

  Future<UserInfo?> getUserInfo(JsonDecoder decoder) async {
    http.Response userInfoResponse =
        await BackendApi.initiateGetCall(ApiUrls.userInfo);
    Map<String, dynamic> userResponse = decoder.convert(userInfoResponse.body);
    return UserInfo.fromJson(userResponse);
  }

  @override
  void dispose() {
    _scrollLogTimer?.cancel();
    super.dispose();
  }
}
