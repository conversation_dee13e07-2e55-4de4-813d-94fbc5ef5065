import 'package:flutter/material.dart';
import 'package:nds_app/companies/b2c/widgets/dashboard/bottom_navigation_b2c.dart';
import 'package:nds_app/companies/lapa/widgets/dashboard/bottom_navigation_lapa.dart';
import 'package:nds_app/companies/nds/widgets/dashboard/bottom_navigation_nds.dart';
import 'package:nds_app/companies/nichesolv/widgets/dashboard/bottom_navigation_nichesolv.dart';
import 'package:nds_app/companies/prodred/widgets/dashboard/bottom_navigation_prodred.dart';
import 'package:nds_app/models/enums/color_type.dart';
import '../branding/branding.dart';

/// Factory class to create company-specific bottom navigation components based on the current company configuration
class BottomNavigationFactory {

  /// Creates and returns the appropriate getBody widget based on the company name
  static Widget createGetBody(
      Function action, BuildContext context, Color color, ColorType colorType) {
    switch (companyName) {
      case 'b2c':
        return getBodyB2C(action, context, color, colorType);
      case 'lapa':
        return getBodyLapa(action, context, color, colorType);
      case 'nds':
        return getBodyNDS(action, context, color, colorType);
      case 'prodred':
        return getBodyProdRed(action, context, color, colorType);
      case 'Nichesolv':
        return getBodyNichesolv(action, context, color, colorType);
      default:
        // Default to NDS if company is not recognized
        return getBodyNDS(action, context, color, colorType);
    }
  }

  /// Creates and returns the appropriate getCustomBottomNavigationBar widget based on the company name
  static Widget createGetCustomBottomNavigationBar(
      BuildContext context, Function action, Color color, ColorType colorType) {
    switch (companyName) {
      case 'b2c':
        return getCustomBottomNavigationBarB2C(context, action, color, colorType);
      case 'lapa':
        return getCustomBottomNavigationBarLapa(context, action, color, colorType);
      case 'nds':
        return getCustomBottomNavigationBarNDS(context, action, color, colorType);
      case 'prodred':
        return getCustomBottomNavigationBarProdRed(context, action, color, colorType);
      case 'Nichesolv':
        return getCustomBottomNavigationBarNichesolv(context, action, color, colorType);
      default:
        // Default to NDS if company is not recognized
        return getCustomBottomNavigationBarNDS(context, action, color, colorType);
    }
  }

  /// Returns the company-specific bottom navigation class name for debugging purposes
  static String getBottomNavigationClassName() {
    switch (companyName) {
      case 'b2c':
        return 'BottomNavigationB2C';
      case 'lapa':
        return 'BottomNavigationLapa';
      case 'nds':
        return 'BottomNavigationNDS';
      case 'prodred':
        return 'BottomNavigationProdRed';
      case 'Nichesolv':
        return 'BottomNavigationNichesolv';
      default:
        return 'BottomNavigationNDS';
    }
  }

  /// Returns a list of all supported company names
  static List<String> getSupportedCompanies() {
    return ['b2c', 'lapa', 'nds', 'prodred', 'Nichesolv'];
  }

  /// Validates if the current company name is supported
  static bool isCompanySupported() {
    return getSupportedCompanies().contains(companyName);
  }
}
